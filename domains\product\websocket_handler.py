"""
WebSocket Handler for Product Flow Sessions.
Handles WebSocket communication for flow-based product operations.
"""

import logging
import json
from fastapi import WebSocket, WebSocketDisconnect
from typing import Optional
import asyncio

from .flow_coordinator import flow_coordinator
from managers.ws_manager import ws_manager
from managers.session_manager import session_manager

logger = logging.getLogger(__name__)

async def handle_product_flow_websocket(
    websocket: WebSocket,
    session_id: str
):
    """Handle WebSocket communication for product flow sessions"""
    
    logger.info(f"Product flow WebSocket connected: {session_id}")
    
    try:
        # Register WebSocket connection for product flow sessions
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Registered WebSocket connection for product flow session: {session_id}")
        logger.info(f"WebSocket connection state: {ws_manager.is_connected(session_id)}")
        
        # Check if we have an active flow for this session
        flow_status = flow_coordinator.get_flow_status(session_id)
        if not flow_status:
            logger.error(f"No active flow found for session: {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "žádný aktivní flow nenalezen",
                "error_code": "NO_ACTIVE_FLOW"
            })
            return
        
        # Send initial flow status
        await ws_manager.send(session_id, {
            "type": "flow_status",
            "status": "connected",
            "current_step": flow_status.get("current_step"),
            "message": "Připojeno k produktovému flow"
        })
        
        # Start flow execution now that WebSocket is connected
        logger.info(f"WebSocket connected, starting flow execution for session {session_id}")
        await flow_coordinator.execute_current_step(session_id)
        
        logger.info(f"Starting message loop for session {session_id}")
        
        # Start step execution in background task
        asyncio.create_task(flow_coordinator.execute_current_step_async(session_id))
        
        # Main message loop
        while ws_manager.is_connected(session_id):
            try:
                logger.info(f"Waiting for message from session {session_id}...")
                message = await websocket.receive_text()
                logger.info(f"Raw WebSocket message received from session {session_id}: '{message}'")
                
                # Skip empty messages
                if not message or message.strip() == "":
                    logger.debug(f"Received empty message from session {session_id}, skipping")
                    continue
                
                try:
                    data = json.loads(message)
                    logger.info(f"Successfully parsed JSON from session {session_id}: {data}")
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON message format from session {session_id}: {e}, message: '{message}'")
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Neplatný formát zprávy (očekáván JSON)",
                        "error_code": "INVALID_JSON"
                    })
                    continue
                
                msg_type = data.get("type")
                
                logger.info(f"Processing product flow message type: {msg_type}")
                
                if msg_type == "ping":
                    await ws_manager.send(session_id, {
                        "type": "pong",
                        "message": "Connection alive"
                    })
                    continue
                
                # Handle flow-specific messages
                success = await flow_coordinator.handle_websocket_message(session_id, data)
                
                if not success:
                    logger.warning(f"Failed to handle message type '{msg_type}' for session {session_id}")
                    await ws_manager.send(session_id, {
                        "type": "message_error",
                        "message": f"Nepodařilo se zpracovat zprávu typu '{msg_type}'",
                        "original_message_type": msg_type
                    })
                
            except WebSocketDisconnect:
                logger.info(f"Product flow WebSocket disconnected: {session_id}")
                break
            except Exception as e:
                logger.error(f"Error processing WebSocket message: {e}")
                await ws_manager.send(session_id, {
                    "type": "error",
                    "message": f"Chyba při zpracování zprávy: {str(e)}",
                    "error_code": "MESSAGE_PROCESSING_ERROR"
                })
    
    except WebSocketDisconnect:
        logger.info(f"Product flow WebSocket disconnected: {session_id}")
    except Exception as e:
        logger.error(f"Unexpected error in product flow WebSocket handler: {e}")
        try:
            await ws_manager.send(session_id, {
                "type": "error",
                "message": f"Neočekávaná chyba: {str(e)}",
                "error_code": "UNEXPECTED_ERROR"
            })
        except:
            pass
    finally:
        # Cleanup WebSocket connection for product flow sessions
        ws_manager.disconnect(session_id)
        logger.info(f"Product flow WebSocket connection closed: {session_id}")

async def handle_product_pickup_websocket(
    websocket: WebSocket,
    session_id: str
):
    """Handle WebSocket communication for product pickup sessions using pickup_process"""

    logger.info(f"Product pickup WebSocket connected: {session_id}")

    try:
        # Register WebSocket connection
        await ws_manager.connect(session_id, websocket)
        logger.info(f"Registered WebSocket connection for product pickup session: {session_id}")

        # Get session data
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for session_id: {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Session not found"
            })
            return

        section_id = getattr(session, 'section_id', None)
        requires_payment = getattr(session, 'amount', 0) > 0
        product_id = getattr(session, 'product_id', None)

        if not section_id:
            logger.error(f"No section_id found in session {session_id}")
            await ws_manager.send(session_id, {
                "type": "error",
                "message": "Invalid session data"
            })
            return

        logger.info(f"Starting product pickup for section {section_id}, requires_payment: {requires_payment}")

        # Create message queue for pickup_process
        message_queue = asyncio.Queue()

        # Message handler task
        async def handle_websocket_messages():
            try:
                while True:
                    message = await websocket.receive_text()
                    logger.info(f"Received WebSocket message for session {session_id}: {message}")

                    try:
                        data = json.loads(message)
                        await message_queue.put(data)
                    except json.JSONDecodeError as e:
                        logger.error(f"Invalid JSON message: {e}")
                        await ws_manager.send(session_id, {
                            "type": "error",
                            "message": "Invalid JSON format"
                        })
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for session {session_id}")
            except Exception as e:
                logger.error(f"Error in message handler: {e}")

        # Start message handler task
        message_task = asyncio.create_task(handle_websocket_messages())

        # Start pickup_process directly from universal process_manager
        from managers.process_manager import pickup_process
        success, successful_sections = await pickup_process(
            sections=[section_id],
            session_id=session_id,
            message_queue=message_queue,
            requires_payment=requires_payment
        )

        # Cancel message handler
        message_task.cancel()

        # Complete product pickup operation
        if success and successful_sections and product_id:
            # Mark product as picked up in database (status=0 means completed/picked up)
            try:
                from infrastructure.repositories.product_repository import ProductRepository
                repo = ProductRepository()
                repo.update_sale_reservation_status(product_id, 0)
                logger.info(f"Marked product {product_id} as picked up after successful pickup")
            except Exception as e:
                logger.error(f"Error marking product as picked up: {e}")

        logger.info(f"Product pickup completed for session {session_id}: success={success}")

    except WebSocketDisconnect:
        logger.info(f"Product pickup WebSocket disconnected: {session_id}")
    except Exception as e:
        logger.error(f"Error in product pickup WebSocket handler: {e}")
        try:
            await ws_manager.send(session_id, {
                "type": "error",
                "message": f"Error in pickup process: {str(e)}"
            })
        except:
            pass
    finally:
        # Cleanup WebSocket connection
        ws_manager.disconnect(session_id)
        logger.info(f"Product pickup WebSocket connection closed: {session_id}")

async def handle_payment_callback(session_id: str, status: str, message: str = "") -> bool:
    """Handle payment callback for product flow sessions"""
    logger.info(f"Handling payment callback for product flow session {session_id}: {status}")

    try:
        # Forward to flow coordinator
        return await flow_coordinator.handle_payment_callback(session_id, status, message)

    except Exception as e:
        logger.error(f"Error handling payment callback for product flow session {session_id}: {e}")
        return False