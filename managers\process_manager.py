"""
Universal Process Manager.
Contains universal functions used by storage, order, and product modules:
- pickup_process: Universal pickup process with payment support
- payment_process: Universal payment process with storno support  
- select_sections: Universal section selection process
"""

import logging
import asyncio
import httpx
from typing import List, Tuple, Dict, Any

from managers.ws_manager import ws_manager
from managers.session_manager import session_manager
from config import device_config

logger = logging.getLogger(__name__)


async def pickup_process(sections: List[int], session_id: str, message_queue: asyncio.Queue, requires_payment: bool = False) -> Tuple[bool, List[int]]:
    """
    Universal pickup process function used by storage, order, and product modules.

    This function handles ALL pickup-related WebSocket messages:
    - payment_screen_ready: processes payment if required
    - hardware_screen_ready: starts FSM sequence for all sections, then ends pickup process
    - open_section: starts FSM sequence for individual section
    - hardware_screen_stop: ends pickup process

    The pickup process also ends when WebSocket connection is closed.

    Args:
        sections: List of section IDs to pickup
        session_id: WebSocket session ID
        message_queue: Queue to receive WebSocket messages
        requires_payment: Whether payment is required before pickup

    Returns:
        Tuple of (success, successful_sections)
    """
    logger.info(f"Starting pickup_process for {len(sections)} sections: {sections}")
    
    picking_up = True
    successful_sections = []
    
    # Import sequence manager for FSM operations
    from managers.sequence_manager import SequenceManager
    from managers.session_manager import SectionConfig
    sequence_manager = SequenceManager()
    
    try:
        # Handle payment if required - wait for payment_screen_ready then call payment terminal
        if requires_payment:
            logger.info("Payment required, starting payment process")
            
            # Use universal payment_process function
            payment_success = await payment_process(session_id, message_queue)
            if not payment_success:
                logger.info("Payment failed or cancelled - ending pickup process")
                return False, []
            
            logger.info("Payment completed successfully - proceeding to pickup loop")
            
        # Send appropriate screen message based on payment requirement
        if requires_payment:
            # After payment, send pickup loop screen
            await ws_manager.send(session_id, {
                "type": "start_hardware_screen",
                "wait_for_ready": True
            })
        else:
            # No payment required, go directly to hardware screen
            await ws_manager.send(session_id, {
                "type": "start_hardware_screen",
                "wait_for_ready": True
            })
        
        # Main pickup loop - handle all message types
        while picking_up:
            try:
                # Wait for message from websocket - no timeout
                message = await message_queue.get()
            except Exception as e:
                logger.info(f"WebSocket disconnected or error waiting for message in pickup_process: {e}")
                # End pickup process when WebSocket disconnects
                logger.info(f"Ending pickup process due to WebSocket disconnection for session {session_id}")
                return True, successful_sections

            message_type = message.get("type")
            logger.info(f"Processing pickup_process message: {message_type}")

            if message_type == "hardware_screen_ready":
                # Start FSM pickup sequence and open all sections automatically each after each
                logger.info(f"Hardware screen ready - starting FSM sequence for {len(sections)} sections")

                # Send acknowledgment that hardware screen ready was received
                await ws_manager.send(session_id, {
                    "type": "hardware_screen_ready_ack",
                    "message": f"Starting pickup sequence for {len(sections)} sections"
                })

                # Convert section IDs to SectionConfig objects
                section_configs = []
                for section_id in sections:
                    section_configs.append(SectionConfig(
                        section_id=section_id,
                        lock_id=section_id,  # Default to same as section_id
                        is_tempered=True,  # Default to tempered for pickup operations
                        led_section=None
                    ))

                # Start FSM sequence - this will open all sections automatically
                success = await sequence_manager.start_fsm_sequence(
                    session_id=session_id,
                    sections=section_configs,
                    pin="pickup_process"
                )

                if success:
                    logger.info(f"FSM sequence completed successfully for {len(sections)} sections")
                    successful_sections.extend(sections)

                    # Send success message to client
                    await ws_manager.send(session_id, {
                        "type": "hardware_result",
                        "success": True,
                        "message": f"Pickup sequence completed for {len(successful_sections)} sections",
                        "sections": successful_sections
                    })
                else:
                    logger.error("FSM sequence failed for pickup process")

                    # Send failure message to client
                    await ws_manager.send(session_id, {
                        "type": "hardware_result",
                        "success": False,
                        "message": "Failed to complete pickup sequence",
                        "sections": []
                    })

                # End pickup process after handling hardware_screen_ready
                logger.info(f"Ending pickup process after hardware_screen_ready for session {session_id}")
                picking_up = False
                return True, successful_sections

            elif message_type == "open_section":
                section_id = message.get("section_id")
                if section_id not in sections:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Invalid section"
                    })
                    continue

                # Use start_fsm_sequence for individual section opening
                logger.info(f"Opening individual section: {section_id}")

                # Send acknowledgment that section opening was received
                await ws_manager.send(session_id, {
                    "type": "open_section_ack",
                    "section_id": section_id,
                    "message": f"Opening section {section_id}"
                })

                section_config = SectionConfig(
                    section_id=section_id,
                    lock_id=section_id,  # Default to same as section_id
                    is_tempered=True,  # Default to tempered for pickup operations
                    led_section=None
                )

                success = await sequence_manager.start_fsm_sequence(
                    session_id=session_id,
                    sections=[section_config],
                    pin="pickup_process_single"
                )

                if success:
                    if section_id not in successful_sections:
                        successful_sections.append(section_id)

                    # Send success message to client
                    await ws_manager.send(session_id, {
                        "type": "section_result",
                        "success": True,
                        "section_id": section_id,
                        "message": f"Section {section_id} opened successfully"
                    })
                else:
                    # Send failure message to client
                    await ws_manager.send(session_id, {
                        "type": "section_result",
                        "success": False,
                        "section_id": section_id,
                        "message": f"Failed to open section {section_id}"
                    })

            elif message_type == "hardware_screen_stop":
                # Stop pickup sequence or end pickup process
                logger.info(f"Received hardware_screen_stop command - ending pickup process for session {session_id}")
                picking_up = False
                return True, successful_sections

        return True, successful_sections
        
    except Exception as e:
        logger.error(f"Error in pickup_process: {e}")
        return False, []


async def payment_process(session_id: str, message_queue: asyncio.Queue) -> bool:
    """
    Universal payment process function that handles payment flow with callbacks.

    Flow:
    1. Sends start_payment_screen message
    2. Waits for payment_screen_ready
    3. Initiates payment terminal call
    4. Waits for payment_status with result
    5. Sends payment_result message

    Args:
        session_id: WebSocket session ID
        message_queue: Queue to receive WebSocket messages

    Returns:
        bool: True if payment successful, False if failed or cancelled
    """
    logger.info(f"Starting payment process for session {session_id}")
    
    try:
        # Send start payment screen message
        await ws_manager.send(session_id, {
            "type": "start_payment_screen",
            "wait_for_ready": True
        })
        
        # Wait for payment_screen_ready or storno
        while True:
            try:
                message = await message_queue.get()
            except Exception as e:
                logger.error(f"Error waiting for payment message: {e}")
                return False

            message_type = message.get("type")
            logger.info(f"Processing payment message: {message_type}")

            if message_type == "payment_screen_ready":
                logger.info("Payment screen ready - starting payment terminal call")

                # Send payment initiation status
                await ws_manager.send(session_id, {
                    "type": "payment_status",
                    "status": "initiating"
                })

                # Make actual payment terminal call (this just initiates the payment)
                payment_initiated = await _process_payment_terminal(session_id)
                if payment_initiated:
                    # Send payment processing status
                    await ws_manager.send(session_id, {
                        "type": "payment_status",
                        "status": "processing"
                    })

                    logger.info(f"Payment initiated successfully for session {session_id}, waiting for callback")
                    # Continue waiting for payment callback - don't return here
                else:
                    # Payment terminal call failed
                    await ws_manager.send(session_id, {
                        "type": "payment_result",
                        "success": False,
                        "message": "Payment failed to initiate"
                    })
                    return False

            elif message_type == "payment_status":
                # Handle payment status from payment service
                logger.info("Received payment status")

                success = message.get("success", False)

                # Send payment result
                await ws_manager.send(session_id, {
                    "type": "payment_result",
                    "success": success,
                    "message": "Payment successful" if success else "Payment failed"
                })

                logger.info(f"Payment status processed for session {session_id}: success={success}")
                return success
                    
            elif message_type == "hardware_screen_stop":
                logger.info("Received hardware_screen_stop during payment - cancelling payment")
                
                # Send payment cancelled result
                await ws_manager.send(session_id, {
                    "type": "payment_result",
                    "success": False,
                    "message": "Payment failed"
                })
                return False
                
            else:
                logger.warning(f"Unexpected message during payment wait: {message_type}")
                
    except Exception as e:
        logger.error(f"Error in payment process: {e}")
        await ws_manager.send(session_id, {
            "type": "payment_result",
            "success": False,
            "message": "Payment failed"
        })
        return False


async def select_sections(available_sections: List[int], session_id: str, message_queue: asyncio.Queue, wait_for_stop: bool = True) -> Tuple[bool, List[int]]:
    """
    Universal section selection function used by order and other modules.
    
    Args:
        available_sections: List of available section IDs to select from
        session_id: WebSocket session ID
        message_queue: Queue to receive WebSocket messages
        wait_for_stop: Whether to wait for stop_selection command (default: True)
        
    Returns:
        Tuple of (success, selected_sections)
    """
    logger.info(f"Starting section selection for session {session_id} with {len(available_sections)} available sections: {available_sections}")
    
    selected_sections = []
    selecting = True
    
    try:
        # Send initial section selection screen message
        await ws_manager.send(session_id, {
            "type": "start_section_selection_screen",
            "available_sections": available_sections,
            "wait_for_ready": True
        })
        
        # Main selection loop - handle section selection messages
        while selecting:
            try:
                # Wait for message from websocket - no timeout
                message = await message_queue.get()
            except Exception as e:
                logger.error(f"Error waiting for message in select_sections: {e}")
                break

            message_type = message.get("type")
            logger.info(f"Processing section selection message: {message_type}")

            if message_type == "open_section":
                section_id = message.get("section_id")
                if section_id not in available_sections:
                    await ws_manager.send(session_id, {
                        "type": "error",
                        "message": "Invalid section"
                    })
                    continue

                # Open the section using FSM sequence
                logger.info(f"Opening section for selection: {section_id}")
                
                # Import sequence manager for FSM operations
                from managers.sequence_manager import SequenceManager
                from managers.session_manager import SectionConfig
                sequence_manager = SequenceManager()
                
                section_config = SectionConfig(
                    section_id=section_id,
                    lock_id=section_id,  # Default to same as section_id
                    is_tempered=True,  # Default to tempered for selection operations
                    led_section=None
                )
                
                success = await sequence_manager.start_fsm_sequence(
                    session_id=session_id,
                    sections=[section_config],
                    pin="section_selection"
                )
                
                if success:
                    # Send waiting for insertion message
                    await ws_manager.send(session_id, {
                        "type": "waiting_for_inserted",
                        "section_id": section_id
                    })
                    
                    # Wait for insertion confirmation
                    while True:
                        try:
                            confirm_message = await message_queue.get()
                            confirm_type = confirm_message.get("type")
                            
                            if confirm_type == "inserted":
                                inserted = confirm_message.get("inserted", False)
                                if inserted:
                                    logger.info(f"Item inserted in section {section_id}")
                                    if section_id not in selected_sections:
                                        selected_sections.append(section_id)
                                else:
                                    logger.info(f"No item inserted in section {section_id}")
                                break
                            elif confirm_type == "storno":
                                logger.info("Received storno during insertion wait")
                                selecting = False
                                break
                            else:
                                logger.warning(f"Unexpected message during insertion wait: {confirm_type}")
                                
                        except Exception as e:
                            logger.error(f"Error waiting for insertion confirmation: {e}")
                            break

            elif message_type == "stop_selection" and wait_for_stop:
                # Stop section selection process
                logger.info(f"Received stop_selection - ending section selection for session {session_id}")
                selecting = False
                return True, selected_sections

            elif message_type == "storno":
                # Cancel section selection process
                logger.info(f"Received storno command - ending section selection for session {session_id}")
                selecting = False
                return True, selected_sections

        return True, selected_sections
        
    except Exception as e:
        logger.error(f"Error in select_sections: {e}")
        return False, []


async def _process_payment_terminal(session_id: str) -> bool:
    """
    Process payment through payment terminal.
    Universal payment function used by all modules.

    Args:
        session_id: WebSocket session ID

    Returns:
        bool: True if payment successful, False otherwise
    """
    try:
        # Get session data for payment amount
        session = session_manager.get_session(session_id)
        if not session:
            logger.error(f"No session found for payment: {session_id}")
            return False

        amount = getattr(session, 'amount', 0)
        if amount <= 0:
            logger.warning(f"No payment amount found for session {session_id}")
            return True  # No payment needed

        # Prepare payment data
        payment_data = {
            "type": "sale",
            "amount": float(amount),
            "variable_symbol": session_id  # Use session_id for unique identification
        }

        logger.info(f"Starting payment processing: {payment_data}")

        # Send payment request to payment service
        try:
            payment_service_timeout = device_config.payment_config.get("payment_service_timeout", 30)
            payment_service_url = device_config.payment_config.get("payment_service_url")

            if not payment_service_url:
                logger.error("Payment service URL not configured")
                return False

            async with httpx.AsyncClient(timeout=payment_service_timeout) as client:
                response = await client.post(
                    payment_service_url,
                    json=payment_data
                )

                if response.status_code == 200:
                    logger.info(f"Payment service response: {response.status_code} - {response.text}")
                    logger.info(f"Payment terminal call successful for session {session_id}")
                    return True
                else:
                    logger.error(f"Payment service error: {response.status_code} - {response.text}")
                    return False

        except httpx.TimeoutException:
            logger.error(f"Payment service timeout for session {session_id}")
            return False

        except Exception as e:
            logger.error(f"Payment service error: {e}")
            return False

    except Exception as e:
        logger.error(f"Error in payment processing: {e}")
        return False
